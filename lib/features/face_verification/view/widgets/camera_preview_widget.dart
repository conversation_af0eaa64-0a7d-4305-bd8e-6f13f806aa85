import 'dart:async';
import 'dart:math' as math;
import 'dart:typed_data';

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/view/widgets/face_guide_overlay.dart';
import 'package:bloomg_flutter/shared/constants/logging_constants.dart';
import 'package:bloomg_flutter/shared/services/logger_service.dart';
import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_mlkit_face_detection/google_mlkit_face_detection.dart';

/// {@template camera_preview_widget}
/// Widget that displays the camera preview with face detection overlay.
///
/// Shows a full-screen camera preview with a face guide overlay
/// and real-time face detection feedback.
/// {@endtemplate}
class CameraPreviewWidget extends StatefulWidget {
  /// {@macro camera_preview_widget}
  const CameraPreviewWidget({super.key});

  @override
  State<CameraPreviewWidget> createState() => _CameraPreviewWidgetState();
}

class _CameraPreviewWidgetState extends State<CameraPreviewWidget> {
  final LoggerService _logger = LoggerService();
  FaceDetector? _faceDetector;
  Timer? _faceDetectionTimer;
  bool _isProcessingFrame = false;
  String? _currentVideoPath;

  @override
  void initState() {
    super.initState();
    _initializeFaceDetector();
  }

  @override
  void dispose() {
    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera preview widget disposing',
      ),
    );

    // Cancel timer first to stop processing
    _faceDetectionTimer?.cancel();
    _faceDetectionTimer = null;

    // Close face detector
    _faceDetector?.close();
    _faceDetector = null;

    // Clear processing flag
    _isProcessingFrame = false;
    _currentVideoPath = null;

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Camera preview widget disposed successfully',
      ),
    );

    super.dispose();
  }

  /// Initializes the face detector for real-time detection with optimized settings
  Future<void> _initializeFaceDetector() async {
    try {
      // Configure face detector for optimal real-time performance
      // Disable expensive features to improve frame processing speed
      final options = FaceDetectorOptions();

      _faceDetector = FaceDetector(options: options);

      _logger.info(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detector initialized with optimized settings',
          'Mode: FAST, MinFaceSize: 0.1, All features disabled for performance',
        ),
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'Face detector initialization failed: $error',
        ),
        error,
        stackTrace,
      );
      rethrow; // Re-throw to prevent silent failures
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<FaceVideoCaptureBloc, FaceVideoCaptureState>(
      builder: (context, state) {
        return Stack(
          children: [
            // Camera preview background
            _buildCameraPreview(context, state),

            // Face guide overlay
            FaceGuideOverlay(
              currentDetection: state.currentDetection,
              isRecording: state is Recording,
            ),
          ],
        );
      },
    );
  }

  /// Builds the camera preview with CamerAwesome integration
  Widget _buildCameraPreview(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    // Handle different states
    if (state is CameraInitializing) {
      return _buildLoadingState();
    }

    if (state is Error) {
      return _buildErrorState(state.errorMessage ?? 'Camera error occurred');
    }

    // Build CamerAwesome widget for camera ready and recording states
    return _buildCameraAwesome(context, state);
  }

  /// Builds the loading state during camera initialization
  Widget _buildLoadingState() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF1A1A1A),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              'Initializing Camera...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the error state when camera fails
  Widget _buildErrorState(String errorMessage) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: const Color(0xFF1A1A1A),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Camera Error',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                errorMessage,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () {
                context
                    .read<FaceVideoCaptureBloc>()
                    .add(const InitializeCamera());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the CamerAwesome widget with face detection integration
  Widget _buildCameraAwesome(
    BuildContext context,
    FaceVideoCaptureState state,
  ) {
    return CameraAwesomeBuilder.awesome(
      saveConfig: SaveConfig.video(),
      sensorConfig: SensorConfig.single(
        sensor: Sensor.position(SensorPosition.front),
        aspectRatio: CameraAspectRatios.ratio_16_9,
      ),
      onMediaTap: _handleVideoCapture,
      onImageForAnalysis: (analysisImage) async {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Frame received for analysis',
            'Size: ${analysisImage.width}x${analysisImage.height}, '
                'Format: ${analysisImage.runtimeType}',
          ),
        );
        _processFrameForFaceDetection(analysisImage);
      },
      imageAnalysisConfig: AnalysisConfig(
        androidOptions: const AndroidAnalysisOptions.nv21(
          width: 480, // Higher resolution for better face detection
        ),
        maxFramesPerSecond:
            8, // Reduced FPS to compensate for higher resolution
      ),
      theme: AwesomeTheme(
        bottomActionsBackgroundColor: Colors.transparent,
        buttonTheme: AwesomeButtonTheme(
          backgroundColor: Colors.transparent,
          iconSize: 0, // Hide default buttons
        ),
      ),
    );
  }

  /// Handles video capture completion
  void _handleVideoCapture(MediaCapture mediaCapture) {
    _currentVideoPath = mediaCapture.captureRequest.when(
      single: (single) => single.file?.path,
      multiple: (multiple) => null,
    );

    _logger.info(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Video capture completed',
        'Path: $_currentVideoPath',
      ),
    );
  }

  /// Processes camera frames for real-time face detection
  void _processFrameForFaceDetection(AnalysisImage analysisImage) {
    // Skip if already processing a frame, detector is null, or widget disposed
    if (_isProcessingFrame || _faceDetector == null || !mounted) {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Frame processing skipped',
          'Processing: $_isProcessingFrame, '
              'Detector: ${_faceDetector != null}, Mounted: $mounted',
        ),
      );
      return;
    }

    _isProcessingFrame = true;
    final frameStartTime = DateTime.now();

    _logger.debug(
      LoggingConstants.formatMessage(
        LoggingConstants.faceVerificationModule,
        'Frame processing started',
        'Size: ${analysisImage.width}x${analysisImage.height}',
      ),
    );

    // Add timeout mechanism for frame processing with increased timeout
    final processingFuture = _convertAndDetectFace(analysisImage).timeout(
      const Duration(milliseconds: 1000), // Increased to 1000ms timeout
      onTimeout: () {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Frame processing timeout',
            'Processing took longer than 1000ms',
          ),
        );
        return null;
      },
    );

    processingFuture.then((result) {
      final processingTime = DateTime.now().difference(frameStartTime);

      if (result != null && mounted) {
        try {
          // Send detection result to BLoC
          context.read<FaceVideoCaptureBloc>().add(ProcessFrame(result));

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'ProcessFrame event sent to BLoC',
              'Faces: ${result.faceCount}, Coverage: '
                  '${result.coveragePercentage.toStringAsFixed(1)}%, '
                  'Time: ${processingTime.inMilliseconds}ms',
            ),
          );
        } catch (error) {
          _logger.warning(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'BLoC access failed during frame processing',
              'Error: $error',
            ),
          );
        }
      } else if (result == null) {
        _logger.debug(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'Frame processing returned null result',
            'Time: ${processingTime.inMilliseconds}ms',
          ),
        );
      }
    }).catchError((Object error) {
      final processingTime = DateTime.now().difference(frameStartTime);
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection frame processing failed',
          'Error: $error, Time: ${processingTime.inMilliseconds}ms',
        ),
      );
    }).whenComplete(() {
      _isProcessingFrame = false;
      final totalTime = DateTime.now().difference(frameStartTime);
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Frame processing completed',
          'Total time: ${totalTime.inMilliseconds}ms',
        ),
      );
    });
  }

  /// Converts AnalysisImage to InputImage and performs face detection
  Future<FaceDetectionResult?> _convertAndDetectFace(
    AnalysisImage analysisImage,
  ) async {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Converting AnalysisImage to InputImage',
          'Format: ${analysisImage.runtimeType}',
        ),
      );

      // Convert AnalysisImage to InputImage
      final inputImage = _convertToInputImage(analysisImage);
      if (inputImage == null) {
        _logger.warning(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'InputImage conversion failed',
            'Returned null for ${analysisImage.runtimeType}',
          ),
        );
        return null;
      }

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Starting ML Kit face detection',
          'Image size: ${inputImage.metadata?.size}',
        ),
      );

      // Perform face detection
      final faces = await _faceDetector!.processImage(inputImage);

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'ML Kit detected faces',
          'Count: ${faces.length}',
        ),
      );

      // Convert ML Kit results to our FaceDetectionResult format
      return _convertToFaceDetectionResult(faces, analysisImage);
    } catch (error) {
      _logger.warning(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Face detection conversion failed',
          'Error: $error',
        ),
      );
      return null;
    }
  }

  /// Converts AnalysisImage to InputImage for ML Kit processing with enhanced validation
  InputImage? _convertToInputImage(AnalysisImage analysisImage) {
    try {
      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'Converting AnalysisImage to InputImage',
          'Size: ${analysisImage.width}x${analysisImage.height}, '
              'Type: ${analysisImage.runtimeType}',
        ),
      );

      // Enhanced format validation before conversion
      if (!_validateAnalysisImage(analysisImage)) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'AnalysisImage validation failed',
            'Invalid image dimensions or format',
          ),
        );
        return null;
      }

      // Use the nv21 property for Android or bgra8888 for iOS
      return analysisImage.when(
        nv21: (nv21Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Processing NV21 image format',
              'Bytes length: ${nv21Image.bytes.length}, '
                  'Expected: ${(analysisImage.width * analysisImage.height * 1.5).round()}',
            ),
          );

          // Enhanced NV21 validation
          if (!_validateNV21Image(nv21Image, analysisImage)) {
            throw ArgumentError('NV21 image validation failed');
          }

          // Create InputImage with corrected metadata for NV21
          final inputImage = InputImage.fromBytes(
            bytes: nv21Image.bytes,
            metadata: InputImageMetadata(
              size: Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.nv21,
              bytesPerRow: analysisImage.width, // Y plane stride for NV21
            ),
          );

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'NV21 InputImage created successfully',
              'Size: ${inputImage.metadata?.size}, Format: ${inputImage.metadata?.format}',
            ),
          );

          return inputImage;
        },
        bgra8888: (bgra8888Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Processing BGRA8888 image format',
              'Bytes length: ${bgra8888Image.bytes.length}, '
                  'Expected: ${analysisImage.width * analysisImage.height * 4}',
            ),
          );

          // Enhanced BGRA8888 validation
          if (!_validateBGRA8888Image(bgra8888Image, analysisImage)) {
            throw ArgumentError('BGRA8888 image validation failed');
          }

          // Create InputImage with corrected metadata for BGRA8888
          final inputImage = InputImage.fromBytes(
            bytes: bgra8888Image.bytes,
            metadata: InputImageMetadata(
              size: Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.bgra8888,
              bytesPerRow:
                  analysisImage.width * 4, // 4 bytes per pixel for BGRA
            ),
          );

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'BGRA8888 InputImage created successfully',
              'Size: ${inputImage.metadata?.size}, '
                  'Format: ${inputImage.metadata?.format}',
            ),
          );

          return inputImage;
        },
        yuv420: (yuv420Image) {
          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'Processing YUV420 image format',
              'Planes count: ${yuv420Image.planes.length}',
            ),
          );

          // Enhanced YUV420 validation
          if (!_validateYUV420Image(yuv420Image, analysisImage)) {
            throw ArgumentError('YUV420 image validation failed');
          }

          // CRITICAL FIX: Properly combine all YUV420 planes
          // Previous implementation only used Y plane which caused detection failures
          final combinedBytes = _combineYUV420Planes(yuv420Image);

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'YUV420 planes combined successfully',
              'Total bytes: ${combinedBytes.length}, '
                  'Y plane: ${yuv420Image.planes[0].bytes.length}, '
                  'U plane: ${yuv420Image.planes.length > 1 ? yuv420Image.planes[1].bytes.length : 0}, '
                  'V plane: ${yuv420Image.planes.length > 2 ? yuv420Image.planes[2].bytes.length : 0}',
            ),
          );

          // Create InputImage with properly combined YUV420 data
          final inputImage = InputImage.fromBytes(
            bytes: combinedBytes,
            metadata: InputImageMetadata(
              size: Size(
                analysisImage.width.toDouble(),
                analysisImage.height.toDouble(),
              ),
              rotation: InputImageRotation.rotation0deg,
              format: InputImageFormat.yuv420,
              bytesPerRow: analysisImage.width,
            ),
          );

          _logger.debug(
            LoggingConstants.formatMessage(
              LoggingConstants.faceVerificationModule,
              'YUV420 InputImage created successfully',
              'Size: ${inputImage.metadata?.size}, '
                  'Format: ${inputImage.metadata?.format}',
            ),
          );

          return inputImage;
        },
      );
    } catch (error, stackTrace) {
      _logger.error(
        LoggingConstants.formatError(
          LoggingConstants.faceVerificationModule,
          LoggingConstants.criticalError,
          'InputImage conversion failed: $error\n'
          'Image format: ${analysisImage.runtimeType}\n'
          'Image size: ${analysisImage.width}x${analysisImage.height}',
        ),
        error,
        stackTrace,
      );
      return null;
    }
  }

  /// Converts ML Kit Face detection results to our FaceDetectionResult format
  FaceDetectionResult _convertToFaceDetectionResult(
    List<Face> faces,
    AnalysisImage analysisImage,
  ) {
    final timestamp = DateTime.now();

    if (faces.isEmpty) {
      return FaceDetectionResult(
        faceDetected: false,
        faceCount: 0,
        coveragePercentage: 0,
        timestamp: timestamp,
      );
    }

    // For face verification, we want exactly one face
    final face = faces.first;
    final faceCount = faces.length;

    // Calculate face coverage percentage
    final coveragePercentage = _calculateFaceCoverage(
      face.boundingBox,
      Size(analysisImage.width.toDouble(), analysisImage.height.toDouble()),
    );

    // Convert ML Kit bounding box to our format
    final boundingBox = FaceBoundingBox(
      left: face.boundingBox.left,
      top: face.boundingBox.top,
      width: face.boundingBox.width,
      height: face.boundingBox.height,
    );

    return FaceDetectionResult(
      faceDetected: true,
      faceCount: faceCount,
      coveragePercentage: coveragePercentage,
      timestamp: timestamp,
      boundingBox: boundingBox,
      confidence: 0.9, // ML Kit doesn't provide confidence, use high value
    );
  }

  /// Calculates face coverage percentage within the face guide area
  double _calculateFaceCoverage(Rect faceBounds, Size imageSize) {
    // Face guide configuration (matching FaceDetectionService)
    const faceGuideWidthRatio = 0.7; // 70% of screen width
    const faceGuideHeightRatio = 0.8; // 80% of screen height

    // Calculate face guide area (centered oval)
    final guideWidth = imageSize.width * faceGuideWidthRatio;
    final guideHeight = imageSize.height * faceGuideHeightRatio;
    final guideLeft = (imageSize.width - guideWidth) / 2;
    final guideTop = (imageSize.height - guideHeight) / 2;

    // Calculate intersection area
    final intersectionLeft = math.max(faceBounds.left, guideLeft);
    final intersectionTop = math.max(faceBounds.top, guideTop);
    final intersectionRight =
        math.min(faceBounds.right, guideLeft + guideWidth);
    final intersectionBottom =
        math.min(faceBounds.bottom, guideTop + guideHeight);

    if (intersectionLeft >= intersectionRight ||
        intersectionTop >= intersectionBottom) {
      return 0; // No intersection
    }

    final intersectionArea = (intersectionRight - intersectionLeft) *
        (intersectionBottom - intersectionTop);
    final guideArea = guideWidth * guideHeight;

    return (intersectionArea / guideArea) * 100.0;
  }

  /// Validates AnalysisImage before conversion
  bool _validateAnalysisImage(AnalysisImage analysisImage) {
    if (analysisImage.width <= 0 || analysisImage.height <= 0) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'AnalysisImage validation failed',
          'Invalid dimensions: ${analysisImage.width}x${analysisImage.height}',
        ),
      );
      return false;
    }
    return true;
  }

  /// Validates NV21 image format and data integrity
  bool _validateNV21Image(dynamic nv21Image, AnalysisImage analysisImage) {
    try {
      // Use reflection to safely access bytes property
      final bytes = nv21Image.bytes as Uint8List?;
      if (bytes == null || bytes.isEmpty) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'NV21 validation failed',
            'Empty or null bytes array',
          ),
        );
        return false;
      }

      // For NV21: Y plane (width*height) + UV plane (width*height/2)
      final expectedSize =
          (analysisImage.width * analysisImage.height * 1.5).round();
      if (bytes.length < expectedSize) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'NV21 validation failed',
            'Insufficient bytes: ${bytes.length} < $expectedSize',
          ),
        );
        return false;
      }

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'NV21 validation passed',
          'Bytes: ${bytes.length}, Expected: $expectedSize',
        ),
      );
      return true;
    } catch (e) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'NV21 validation error',
          'Failed to access bytes property: $e',
        ),
      );
      return false;
    }
  }

  /// Validates BGRA8888 image format and data integrity
  bool _validateBGRA8888Image(
    dynamic bgra8888Image,
    AnalysisImage analysisImage,
  ) {
    try {
      // Use reflection to safely access bytes property
      final bytes = bgra8888Image.bytes as Uint8List?;
      if (bytes == null || bytes.isEmpty) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'BGRA8888 validation failed',
            'Empty or null bytes array',
          ),
        );
        return false;
      }

      final expectedSize = analysisImage.width * analysisImage.height * 4;
      if (bytes.length < expectedSize) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'BGRA8888 validation failed',
            'Insufficient bytes: ${bytes.length} < $expectedSize',
          ),
        );
        return false;
      }

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'BGRA8888 validation passed',
          'Bytes: ${bytes.length}, Expected: $expectedSize',
        ),
      );
      return true;
    } catch (e) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'BGRA8888 validation error',
          'Failed to access bytes property: $e',
        ),
      );
      return false;
    }
  }

  /// Validates YUV420 image format and data integrity
  bool _validateYUV420Image(dynamic yuv420Image, AnalysisImage analysisImage) {
    try {
      // Use reflection to safely access planes property
      final planes = yuv420Image.planes as List?;
      if (planes == null || planes.isEmpty) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'YUV420 validation failed',
            'No planes available',
          ),
        );
        return false;
      }

      // Check Y plane (first plane)
      final yPlane = planes.first;
      final yBytes = yPlane.bytes as Uint8List?;
      if (yBytes == null || yBytes.isEmpty) {
        _logger.error(
          LoggingConstants.formatMessage(
            LoggingConstants.faceVerificationModule,
            'YUV420 validation failed',
            'Y plane has empty bytes',
          ),
        );
        return false;
      }

      _logger.debug(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'YUV420 validation passed',
          'Planes: ${planes.length}, Y bytes: ${yBytes.length}',
        ),
      );
      return true;
    } catch (e) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'YUV420 validation error',
          'Failed to access planes property: $e',
        ),
      );
      return false;
    }
  }

  /// Properly combines YUV420 planes for ML Kit processing
  Uint8List _combineYUV420Planes(dynamic yuv420Image) {
    try {
      final planes = yuv420Image.planes as List;
      final allBytes = <int>[];

      // Add Y plane (luminance)
      final yPlane = planes[0];
      final yBytes = yPlane.bytes as Uint8List;
      allBytes.addAll(yBytes);

      // Add U and V planes (chrominance) if available
      if (planes.length > 1) {
        final uPlane = planes[1];
        final uBytes = uPlane.bytes as Uint8List;
        allBytes.addAll(uBytes);
      }

      if (planes.length > 2) {
        final vPlane = planes[2];
        final vBytes = vPlane.bytes as Uint8List;
        allBytes.addAll(vBytes);
      }

      return Uint8List.fromList(allBytes);
    } catch (e) {
      _logger.error(
        LoggingConstants.formatMessage(
          LoggingConstants.faceVerificationModule,
          'YUV420 plane combination failed',
          'Error: $e',
        ),
      );
      // Fallback: return empty bytes
      return Uint8List(0);
    }
  }
}
